// 学科管理端到端测试
// 使用Playwright **仅** 模拟用户浏览器行为，不进行后端结果断言

import { test, expect } from '@playwright/test'

test.describe('学科管理 - 完整用户故事', () => {

  test.beforeEach(async ({ page }) => {
    // 设置视口大小
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('用户创建和查看学科的完整流程', async ({ page }) => {
    console.log('🎬 开始完整用户故事测试')

    // 1. 访问首页
    console.log('📍 步骤1: 访问首页')
    await page.goto('/')

    // 等待页面加载完成
    await page.waitForLoadState('networkidle')

    // 2. 点击学科管理链接进入学科管理页面
    console.log('📍 步骤2: 进入学科管理页面')
    const subjectsLink = page.locator('[data-testid="subjects-link"], a:has-text("学科管理")')
    await subjectsLink.first().click()

    // 等待学科管理页面加载
    await page.waitForLoadState('networkidle')

    // 验证学科管理页面元素存在（仅UI验证，不验证数据）
    await expect(page.locator('[data-testid="subject-list-container"], .subject-manager')).toBeVisible({ timeout: 10000 })

    // 3. 点击创建学科按钮
    console.log('📍 步骤3: 点击创建学科按钮')
    const createButton = page.locator('.create-subject-btn, [data-testid="create-subject-btn"], button:has-text("创建学科"), button:has-text("添加学科"), button:has-text("创建第一个学科")')
    await createButton.first().click()

    // 等待弹窗出现
    await page.waitForTimeout(500)

    // 验证创建弹窗出现
    const modal = page.locator('.create-subject-modal, [data-testid="create-subject-modal"], .ant-modal, .modal')
    await expect(modal.first()).toBeVisible({ timeout: 5000 })

    // 3. 填写学科信息
    console.log('📍 步骤3: 填写学科信息')
    const timestamp = Date.now()
    const subjectName = `E2E测试学科_${timestamp}`
    const subjectDescription = `这是E2E测试创建的学科描述_${timestamp}`

    // 填写学科名称
    const nameInput = page.locator('input[name="name"], input[placeholder*="名称"], input[placeholder*="学科"]')
    await nameInput.first().fill(subjectName)

    // 填写学科描述（如果存在）
    const descInput = page.locator('textarea[name="description"], textarea[placeholder*="描述"], input[name="description"]')
    if (await descInput.first().isVisible()) {
      await descInput.first().fill(subjectDescription)
    }

    // 4. 提交创建
    console.log('📍 步骤4: 提交创建表单')
    const submitButton = page.locator('.submit-btn, [data-testid="submit-btn"], button:has-text("确定"), button:has-text("创建"), button:has-text("提交")')
    await submitButton.first().click()

    // 等待提交处理
    await page.waitForTimeout(1000)

    // 5. 验证成功提示出现（UI层面）
    console.log('📍 步骤5: 等待成功提示')
    const successMessage = page.locator('.success-message, .ant-message-success, .message-success, [data-testid="success-message"]')

    // 等待成功消息或弹窗关闭
    try {
      await expect(successMessage.first()).toBeVisible({ timeout: 3000 })
    } catch {
      // 如果没有成功消息，检查弹窗是否关闭
      await expect(modal.first()).not.toBeVisible({ timeout: 3000 })
    }

    // 6. 验证学科出现在列表中（UI层面）
    console.log('📍 步骤6: 验证学科出现在列表中')
    await page.waitForTimeout(1000) // 等待列表刷新

    // 查找包含新创建学科名称的卡片
    const subjectCard = page.locator(`.subject-card:has-text("${subjectName}"), [data-testid="subject-card"]:has-text("${subjectName}"), .card:has-text("${subjectName}")`)
    await expect(subjectCard.first()).toBeVisible({ timeout: 5000 })

    // 7. 点击学科卡片
    console.log('📍 步骤7: 点击学科卡片')
    await subjectCard.first().click()

    // 等待页面响应
    await page.waitForTimeout(500)

    console.log('✅ 完整用户故事测试完成')
  })

  test('创建重复名称学科的用户流程', async ({ page }) => {
    console.log('🎬 开始重复名称测试')

    // 访问首页
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // 进入学科管理页面
    const subjectsLink = page.locator('[data-testid="subjects-link"], a:has-text("学科管理")')
    await subjectsLink.first().click()
    await page.waitForLoadState('networkidle')

    // 点击创建按钮
    const createButton = page.locator('.create-subject-btn, [data-testid="create-subject-btn"], button:has-text("创建学科"), button:has-text("创建第一个学科")')
    await createButton.first().click()

    // 等待弹窗
    await page.waitForTimeout(500)

    // 填写已存在的学科名称
    const nameInput = page.locator('input[name="name"], input[placeholder*="名称"]')
    await nameInput.first().fill('数学') // 使用已知存在的学科名称

    // 提交
    const submitButton = page.locator('.submit-btn, button:has-text("确定"), button:has-text("创建")')
    await submitButton.first().click()

    // 等待错误提示
    await page.waitForTimeout(1000)

    // 验证错误提示出现（UI层面）
    const errorMessage = page.locator('.error-message, .ant-message-error, .message-error, [data-testid="error-message"]')
    try {
      await expect(errorMessage.first()).toBeVisible({ timeout: 3000 })
    } catch {
      // 如果没有明确的错误消息，检查表单是否仍然可见（表示提交失败）
      const modal = page.locator('.create-subject-modal, .ant-modal')
      await expect(modal.first()).toBeVisible()
    }

    console.log('✅ 重复名称测试完成')
  })

  test('空名称学科创建的用户流程', async ({ page }) => {
    console.log('🎬 开始空名称测试')

    // 访问首页
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // 进入学科管理页面
    const subjectsLink = page.locator('[data-testid="subjects-link"], a:has-text("学科管理")')
    await subjectsLink.first().click()
    await page.waitForLoadState('networkidle')

    // 点击创建按钮
    const createButton = page.locator('.create-subject-btn, [data-testid="create-subject-btn"], button:has-text("创建学科"), button:has-text("创建第一个学科")')
    await createButton.first().click()

    // 等待弹窗
    await page.waitForTimeout(500)

    // 不填写名称，直接提交
    const submitButton = page.locator('.submit-btn, button:has-text("确定"), button:has-text("创建")')
    await submitButton.first().click()

    // 等待验证提示
    await page.waitForTimeout(500)

    // 验证表单验证提示或弹窗仍然存在
    const modal = page.locator('.create-subject-modal, .ant-modal')
    await expect(modal.first()).toBeVisible()

    // 检查是否有验证错误提示
    const validationError = page.locator('.ant-form-item-explain-error, .form-error, .validation-error')
    try {
      await expect(validationError.first()).toBeVisible({ timeout: 2000 })
    } catch {
      // 如果没有明确的验证错误，至少确保弹窗没有关闭
      console.log('表单验证阻止了空名称提交')
    }

    console.log('✅ 空名称测试完成')
  })
})

test.describe('响应式布局测试', () => {

  test('桌面端布局测试', async ({ page }) => {
    console.log('🖥️ 测试桌面端布局')

    // 设置桌面端视口
    await page.setViewportSize({ width: 1920, height: 1080 })

    await page.goto('/subjects')
    await page.waitForLoadState('networkidle')

    // 验证学科管理页面容器存在
    const subjectManager = page.locator('.subject-manager, [data-testid="subject-list-container"]')
    await expect(subjectManager.first()).toBeVisible()

    // 验证多列布局（桌面端应该有多列）
    const subjectCards = page.locator('.subject-card, [data-testid="subject-card"], .card')
    const cardCount = await subjectCards.count()

    if (cardCount > 0) {
      // 检查第一个卡片的位置和大小
      const firstCard = subjectCards.first()
      const boundingBox = await firstCard.boundingBox()
      expect(boundingBox).toBeTruthy()
    }

    console.log('✅ 桌面端布局测试完成')
  })

  test('平板端布局测试', async ({ page }) => {
    console.log('📱 测试平板端布局')

    // 设置平板端视口
    await page.setViewportSize({ width: 768, height: 1024 })

    await page.goto('/subjects')
    await page.waitForLoadState('networkidle')

    // 验证学科管理页面在平板端正常显示
    const subjectManager = page.locator('.subject-manager, [data-testid="subject-list-container"]')
    await expect(subjectManager.first()).toBeVisible()

    console.log('✅ 平板端布局测试完成')
  })

  test('移动端布局测试', async ({ page }) => {
    console.log('📱 测试移动端布局')

    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto('/subjects')
    await page.waitForLoadState('networkidle')

    // 验证学科管理页面在移动端正常显示
    const subjectManager = page.locator('.subject-manager, [data-testid="subject-list-container"]')
    await expect(subjectManager.first()).toBeVisible()

    // 验证创建按钮在移动端可见
    const createButton = page.locator('.create-subject-btn, [data-testid="create-subject-btn"], button:has-text("创建学科")')
    await expect(createButton.first()).toBeVisible()

    console.log('✅ 移动端布局测试完成')
  })
})

test.describe('页面加载性能测试', () => {

  test('学科管理页面加载性能测试', async ({ page }) => {
    console.log('⚡ 测试学科管理页面加载性能')

    const startTime = Date.now()

    await page.goto('/subjects')
    await page.waitForLoadState('networkidle')

    const endTime = Date.now()
    const loadTime = endTime - startTime

    console.log(`📊 页面加载时间: ${loadTime}ms`)

    // 验证页面在合理时间内加载完成（不超过5秒）
    expect(loadTime).toBeLessThan(5000)

    // 验证关键元素已加载
    const subjectManager = page.locator('.subject-manager, [data-testid="subject-list-container"]')
    await expect(subjectManager.first()).toBeVisible()

    console.log('✅ 学科管理页面加载性能测试完成')
  })
})
